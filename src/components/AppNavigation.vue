<template>
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex-shrink-0 flex items-center">
          <router-link to="/" class="flex items-center">
            <img class="h-8 w-auto" src="/logo.png" alt="Jenexus Holding" />
            <span class="ml-2 text-xl font-bold text-primary-700">Jenexus Holding</span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <!-- About Us Dropdown -->
            <div class="relative group">
              <button class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                About Us
                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div class="absolute left-0 mt-2 w-64 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <div class="py-1">
                  <router-link to="/about/company-overview" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Company Overview</router-link>
                  <router-link to="/about/compliance-certifications" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Compliance & Certifications</router-link>
                  <router-link to="/about/affiliates-partners" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Our Affiliates & Partners</router-link>
                  <router-link to="/about/our-client" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Our Client</router-link>
                  <router-link to="/about/qms-quality" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">QMS Quality Objectives & Policy</router-link>
                </div>
              </div>
            </div>

            <!-- Products & Services Dropdown -->
            <div class="relative group">
              <button class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                Our Products & Services
                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <div class="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <div class="py-1">
                  <router-link to="/products/wan-connectivity" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">WAN Connectivity</router-link>
                  <router-link to="/products/secure-gateway" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Secure Gateway</router-link>
                  <router-link to="/products/cloud" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Cloud</router-link>
                  <router-link to="/products/managed-services" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Managed Services Portal</router-link>
                </div>
              </div>
            </div>

            <!-- Single Links -->
            <router-link to="/data-centre" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">Our Data Centre and Facilities</router-link>
            <router-link to="/news-events" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">News & Events</router-link>
            <router-link to="/contact" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">Contact Us</router-link>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path v-if="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div v-if="mobileMenuOpen" class="md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
        <!-- About Us Mobile -->
        <div>
          <button @click="aboutUsOpen = !aboutUsOpen" class="w-full text-left text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium flex items-center justify-between">
            About Us
            <svg class="h-4 w-4" :class="{ 'rotate-180': aboutUsOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div v-if="aboutUsOpen" class="pl-4 space-y-1">
            <router-link to="/about/company-overview" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">Company Overview</router-link>
            <router-link to="/about/compliance-certifications" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">Compliance & Certifications</router-link>
            <router-link to="/about/affiliates-partners" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">Our Affiliates & Partners</router-link>
            <router-link to="/about/our-client" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">Our Client</router-link>
            <router-link to="/about/qms-quality" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">QMS Quality Objectives & Policy</router-link>
          </div>
        </div>

        <!-- Products & Services Mobile -->
        <div>
          <button @click="productsOpen = !productsOpen" class="w-full text-left text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium flex items-center justify-between">
            Our Products & Services
            <svg class="h-4 w-4" :class="{ 'rotate-180': productsOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div v-if="productsOpen" class="pl-4 space-y-1">
            <router-link to="/products/wan-connectivity" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">WAN Connectivity</router-link>
            <router-link to="/products/secure-gateway" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">Secure Gateway</router-link>
            <router-link to="/products/cloud" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">Cloud</router-link>
            <router-link to="/products/managed-services" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600">Managed Services Portal</router-link>
          </div>
        </div>

        <!-- Single Links Mobile -->
        <router-link to="/data-centre" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">Our Data Centre and Facilities</router-link>
        <router-link to="/news-events" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">News & Events</router-link>
        <router-link to="/contact" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">Contact Us</router-link>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const mobileMenuOpen = ref(false)
const aboutUsOpen = ref(false)
const productsOpen = ref(false)
</script>

<style scoped>
.rotate-180 {
  transform: rotate(180deg);
}
</style>
