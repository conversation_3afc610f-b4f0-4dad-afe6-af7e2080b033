<script setup lang="ts">
import { ref } from 'vue'

const products = ref([
  {
    title: 'WAN Connectivity',
    description: 'Reliable and secure wide area network solutions for enterprise connectivity.',
    icon: '🌐',
    link: '/products/wan-connectivity'
  },
  {
    title: 'Secure Gateway',
    description: 'Advanced security gateway solutions to protect your network infrastructure.',
    icon: '🔒',
    link: '/products/secure-gateway'
  },
  {
    title: 'Cloud Services',
    description: 'Scalable cloud infrastructure and services for modern business needs.',
    icon: '☁️',
    link: '/products/cloud'
  },
  {
    title: 'Managed Services',
    description: 'Comprehensive managed services portal for streamlined operations.',
    icon: '⚙️',
    link: '/products/managed-services'
  }
])

const certificates = ref([
  { name: 'ISO 27001', logo: '/certs/iso27001.png' },
  { name: 'ISO 9001', logo: '/certs/iso9001.png' },
  { name: 'MSC Status', logo: '/certs/msc.png' },
  { name: 'SIRIM', logo: '/certs/sirim.png' }
])
</script>

<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white">
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 class="text-4xl lg:text-6xl font-bold mb-6">
              Enterprise Connectivity Solutions
            </h1>
            <p class="text-xl lg:text-2xl mb-8 text-primary-100">
              Leading provider of secure network infrastructure, cloud services, and managed solutions for businesses across Malaysia.
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
              <router-link to="/products/wan-connectivity" class="inline-flex items-center px-8 py-3 bg-white text-primary-700 font-semibold rounded-lg hover:bg-primary-50 transition-colors">
                Explore Our Products
                <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </router-link>
              <router-link to="/contact" class="inline-flex items-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-primary-700 transition-colors">
                Contact Us
              </router-link>
            </div>
          </div>
          <div class="hidden lg:block">
            <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Network Infrastructure" class="rounded-lg shadow-2xl" />
          </div>
        </div>
      </div>
    </section>

    <!-- Certificates Section -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Our Certifications & Compliance</h2>
          <p class="text-lg text-gray-600">Trusted by industry standards and regulatory bodies</p>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 items-center">
          <div v-for="cert in certificates" :key="cert.name" class="flex flex-col items-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
              <span class="text-2xl font-bold text-primary-600">{{ cert.name.substring(0, 2) }}</span>
            </div>
            <h3 class="text-sm font-semibold text-gray-900 text-center">{{ cert.name }}</h3>
          </div>
        </div>
      </div>
    </section>

    <!-- Products & Services Cards -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Products & Services</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive technology solutions designed to empower your business with reliable connectivity, security, and cloud infrastructure.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div v-for="product in products" :key="product.title" class="group cursor-pointer" @click="$router.push(product.link)">
            <div class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform group-hover:-translate-y-2 p-8 border border-gray-100">
              <div class="text-4xl mb-6">{{ product.icon }}</div>
              <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary-600 transition-colors">{{ product.title }}</h3>
              <p class="text-gray-600 mb-6">{{ product.description }}</p>
              <div class="flex items-center text-primary-600 font-semibold group-hover:text-primary-700">
                Learn More
                <svg class="ml-2 h-4 w-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-20 bg-primary-600">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl font-bold text-white mb-6">Ready to Transform Your Business?</h2>
        <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
          Get in touch with our experts to discuss how our solutions can help your organization achieve its technology goals.
        </p>
        <router-link to="/contact" class="inline-flex items-center px-8 py-4 bg-white text-primary-700 font-semibold rounded-lg hover:bg-primary-50 transition-colors text-lg">
          Start Your Journey
          <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </router-link>
      </div>
    </section>
  </div>
</template>
